// Cart page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load cart items
    loadCartItems();

    // Setup event listeners
    setupCartEventListeners();
});

// Setup cart page event listeners
function setupCartEventListeners() {
    // Checkout button
    const checkoutBtn = document.getElementById('checkout-btn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', openCheckoutModal);
    }

    // Checkout form
    const checkoutForm = document.getElementById('checkout-form');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', processCheckout);
    }

    // Modal close button
    const closeModalBtn = document.querySelector('.close');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeCheckoutModal);
    }

    // Close modal when clicking outside
    const modal = document.getElementById('checkout-modal');
    if (modal) {
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeCheckoutModal();
            }
        });
    }
}

// Load cart items
function loadCartItems() {
    const cartItemsContainer = document.getElementById('cart-items');
    const emptyCart = document.getElementById('empty-cart');

    if (!cartItemsContainer || !emptyCart) {
        return;
    }

    // Clear container
    cartItemsContainer.innerHTML = '';

    // Check if cart is empty
    if (cart.length === 0) {
        emptyCart.style.display = 'block';
        updateCartSummary();
        return;
    }

    emptyCart.style.display = 'none';

    // Add cart items to container
    cart.forEach(item => {
        const cartItem = createCartItem(item);
        cartItemsContainer.appendChild(cartItem);
    });

    // Update cart summary
    updateCartSummary();
}

// Create cart item
function createCartItem(item) {
    const cartItem = document.createElement('div');
    cartItem.className = 'cart-item';
    cartItem.setAttribute('data-id', item.id);

    cartItem.innerHTML = `
        <img src="${item.image}" alt="${item.name}">
        <div class="cart-item-info">
            <h3>${item.name}</h3>
            <div class="price">${formatPrice(item.price)}</div>
        </div>
        <div class="cart-item-actions">
            <div class="quantity-control">
                <button class="decrease-quantity">-</button>
                <input type="number" class="item-quantity" value="${item.quantity}" min="1">
                <button class="increase-quantity">+</button>
            </div>
            <button class="remove-item"><i class="fas fa-trash"></i></button>
        </div>
    `;

    // Add event listeners
    const decreaseBtn = cartItem.querySelector('.decrease-quantity');
    const increaseBtn = cartItem.querySelector('.increase-quantity');
    const quantityInput = cartItem.querySelector('.item-quantity');
    const removeBtn = cartItem.querySelector('.remove-item');

    decreaseBtn.addEventListener('click', function() {
        const currentQuantity = parseInt(quantityInput.value);
        if (currentQuantity > 1) {
            quantityInput.value = currentQuantity - 1;
            updateCartItemQuantity(item.id, currentQuantity - 1);
            loadCartItems();
        }
    });

    increaseBtn.addEventListener('click', function() {
        const currentQuantity = parseInt(quantityInput.value);
        quantityInput.value = currentQuantity + 1;
        updateCartItemQuantity(item.id, currentQuantity + 1);
        loadCartItems();
    });

    quantityInput.addEventListener('change', function() {
        const newQuantity = parseInt(this.value);
        if (newQuantity > 0) {
            updateCartItemQuantity(item.id, newQuantity);
            loadCartItems();
        } else {
            this.value = 1;
            updateCartItemQuantity(item.id, 1);
            loadCartItems();
        }
    });

    removeBtn.addEventListener('click', function() {
        removeFromCart(item.id);
        loadCartItems();
    });

    return cartItem;
}

// Update cart summary
function updateCartSummary() {
    const subtotal = calculateCartTotal();
    const shipping = subtotal > 0 ? 20 : 0; // Free shipping for empty cart
    const total = subtotal + shipping;

    // Update summary in cart page
    const subtotalElement = document.getElementById('subtotal');
    const shippingElement = document.getElementById('shipping');
    const totalElement = document.getElementById('total');

    if (subtotalElement) {
        subtotalElement.textContent = formatPrice(subtotal);
    }

    if (shippingElement) {
        shippingElement.textContent = formatPrice(shipping);
    }

    if (totalElement) {
        totalElement.textContent = formatPrice(total);
    }

    // Update summary in checkout modal
    const modalSubtotal = document.getElementById('modal-subtotal');
    const modalShipping = document.getElementById('modal-shipping');
    const modalTotal = document.getElementById('modal-total');

    if (modalSubtotal) {
        modalSubtotal.textContent = formatPrice(subtotal);
    }

    if (modalShipping) {
        modalShipping.textContent = formatPrice(shipping);
    }

    if (modalTotal) {
        modalTotal.textContent = formatPrice(total);
    }
}

// Open checkout modal
function openCheckoutModal() {
    if (cart.length === 0) {
        showNotification('السلة فارغة، يرجى إضافة منتجات أولاً');
        return;
    }

    // Check if user is logged in
    if (!currentUser) {
        showNotification('يرجى تسجيل الدخول أولاً');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1500);
        return;
    }

    const modal = document.getElementById('checkout-modal');
    if (modal) {
        modal.style.display = 'block';

        // Pre-fill form with user data if available
        if (currentUser) {
            document.getElementById('name').value = currentUser.name || '';
            document.getElementById('email').value = currentUser.email || '';
            document.getElementById('phone').value = currentUser.phone || '';
        }
    }
}

// Close checkout modal
function closeCheckoutModal() {
    const modal = document.getElementById('checkout-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Process checkout
function processCheckout(e) {
    e.preventDefault();

    // Get form data
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    const address = document.getElementById('address').value;
    const city = document.getElementById('city').value;
    const payment = document.getElementById('payment').value;

    // Calculate totals
    const subtotal = calculateCartTotal();
    const shipping = subtotal > 0 ? 20 : 0;
    const total = subtotal + shipping;

    // Create order
    const order = {
        id: generateId(),
        userId: currentUser ? currentUser.id : null,
        customerName: name,
        customerEmail: email,
        customerPhone: phone,
        customerAddress: address,
        customerCity: city,
        paymentMethod: payment,
        items: [...cart],
        subtotal: subtotal,
        shipping: shipping,
        total: total,
        status: 'pending',
        date: new Date().toISOString()
    };

    // Add order to orders array
    orders.push(order);

    // Clear cart
    cart = [];

    // Save data
    saveData();

    // Update cart count
    updateCartCount();

    // Close modal
    closeCheckoutModal();

    // Show success message
    showNotification('تم إنشاء الطلب بنجاح! سيتم توجيهك إلى صفحة الطلبات.');

    // Redirect to orders page after a delay
    setTimeout(() => {
        window.location.href = 'orders.html';
    }, 2000);
}