// Global variables
let cart = [];
let products = [];
let orders = [];
let users = [];
let currentUser = null;
let isAdmin = false;

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    // Load data from localStorage
    loadData();

    // Update cart count
    updateCartCount();

    // Check if user is logged in
    checkUserLogin();

    // Setup event listeners
    setupEventListeners();
});

// Load data from localStorage
function loadData() {
    // Load cart
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
        cart = JSON.parse(savedCart);
    }

    // Load products
    const savedProducts = localStorage.getItem('products');
    if (savedProducts) {
        products = JSON.parse(savedProducts);
    } else {
        // Initialize with sample products if none exist
        initializeSampleProducts();
    }

    // Load orders
    const savedOrders = localStorage.getItem('orders');
    if (savedOrders) {
        orders = JSON.parse(savedOrders);
    }

    // Load users
    const savedUsers = localStorage.getItem('users');
    if (savedUsers) {
        users = JSON.parse(savedUsers);
    } else {
        // Initialize with sample users if none exist
        initializeSampleUsers();
    }

    // Load current user
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        isAdmin = currentUser && currentUser.role === 'admin';
    }
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('cart', JSON.stringify(cart));
    localStorage.setItem('products', JSON.stringify(products));
    localStorage.setItem('orders', JSON.stringify(orders));
    localStorage.setItem('users', JSON.stringify(users));
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
}

// Initialize sample products
function initializeSampleProducts() {
    products = [
        {
            id: 1,
            name: "قميص أبيض كلاسيكي",
            category: "men",
            price: 120,
            stock: 15,
            description: "قميص أبيض كلاسيكي مصنوع من القطن عالي الجودة، مثالي للمناسبات الرسمية وغير الرسمية.",
            image: "https://via.placeholder.com/300x400?text=White+Shirt"
        },
        {
            id: 2,
            name: "بنطلون جينز أزرق",
            category: "men",
            price: 180,
            stock: 10,
            description: "بنطلون جينز أزرق داكن مريح وعصري، يناسب جميع الأوقات.",
            image: "https://via.placeholder.com/300x400?text=Blue+Jeans"
        },
        {
            id: 3,
            name: "فستان أسود أنيق",
            category: "women",
            price: 250,
            stock: 8,
            description: "فستان أسود أنيق ومثالي للمناسبات الخاصة والحفلات.",
            image: "https://via.placeholder.com/300x400?text=Black+Dress"
        },
        {
            id: 4,
            name: "بلوزة حريرية",
            category: "women",
            price: 200,
            stock: 12,
            description: "بلوزة حريرية ناعمة ومريحة، مثالية للإطلالات اليومية.",
            image: "https://via.placeholder.com/300x400?text=Silk+Blouse"
        },
        {
            id: 5,
            name: "تي شيرت أطفال كرتوني",
            category: "children",
            price: 70,
            stock: 20,
            description: "تي شيرت أطفال بأشكال كرتونية محبوبة، مصنوع من قطن ناعم على البشرة.",
            image: "https://via.placeholder.com/300x400?text=Kids+T-Shirt"
        },
        {
            id: 6,
            name: "بنطلون أطفال رياضي",
            category: "children",
            price: 90,
            stock: 15,
            description: "بنطلون أطفال رياضي مريح ومثالي للعب والنشاطات اليومية.",
            image: "https://via.placeholder.com/300x400?text=Kids+Sports+Pants"
        },
        {
            id: 7,
            name: "جاكيت جلد",
            category: "men",
            price: 450,
            stock: 5,
            description: "جاكيت جلد عالي الجودة، يضيف لمسة عصرية وجريئة لإطلالتك.",
            image: "https://via.placeholder.com/300x400?text=Leather+Jacket"
        },
        {
            id: 8,
            name: "تنورة صيفية",
            category: "women",
            price: 150,
            stock: 10,
            description: "تنورة صيفية خفيفة ومريحة، مثالية للأيام الدافئة.",
            image: "https://via.placeholder.com/300x400?text=Summer+Skirt"
        }
    ];

    localStorage.setItem('products', JSON.stringify(products));
}

// Initialize sample users
function initializeSampleUsers() {
    users = [
        {
            id: 1,
            name: "مدير النظام",
            email: "<EMAIL>",
            password: "hala123",
            phone: "0500000000",
            role: "admin"
        },
        {
            id: 2,
            name: "أحمد محمد",
            email: "<EMAIL>",
            password: "hala123",
            phone: "0501111111",
            role: "user"
        },
        {
            id: 3,
            name: "فاطمة علي",
            email: "<EMAIL>",
            password: "hala123",
            phone: "0502222222",
            role: "user"
        }
    ];

    localStorage.setItem('users', JSON.stringify(users));
}

// Update cart count
function updateCartCount() {
    const cartCount = document.getElementById('cart-count');
    if (cartCount) {
        cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
    }
}

// Check if user is logged in
function checkUserLogin() {
    const adminLink = document.getElementById('admin-link');
    const loginLink = document.getElementById('login-link');

    // Check if we have a user in localStorage or sessionStorage
    if (!currentUser) {
        const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
        if (savedUser) {
            currentUser = JSON.parse(savedUser);
            isAdmin = currentUser && currentUser.role === 'admin';
        }
    }

    if (currentUser) {
        // Update login link to logout
        const loginLinks = document.querySelectorAll('a[href="login.html"], #login-link');
        loginLinks.forEach(link => {
            link.textContent = 'تسجيل الخروج';
            link.href = '#';
            link.addEventListener('click', logout);
        });

        // Show admin link if user is admin
        if (isAdmin && adminLink) {
            adminLink.style.display = 'block';

            // If admin is on homepage, redirect to admin dashboard
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                window.location.href = 'admin.html';
            }
        }
    }
}

// Setup event listeners
function setupEventListeners() {
    // Cart functionality will be set up in cart.js
    // Product functionality will be set up in products.js
    // Auth functionality will be set up in auth.js
    // Admin functionality will be set up in admin.js
}

// Logout function
function logout(e) {
    e.preventDefault();
    currentUser = null;
    isAdmin = false;
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');

    // Show notification before redirecting
    showNotification('تم تسجيل الخروج بنجاح');

    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Add to cart function
function addToCart(productId, quantity = 1) {
    const product = products.find(p => p.id === productId);

    if (!product) {
        return;
    }

    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: quantity
        });
    }

    saveData();
    updateCartCount();

    // Show notification
    showNotification('تمت إضافة المنتج إلى السلة بنجاح');
}

// Remove from cart function
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveData();
    updateCartCount();
}

// Update cart item quantity
function updateCartItemQuantity(productId, quantity) {
    const item = cart.find(item => item.id === productId);

    if (item) {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            item.quantity = quantity;
            saveData();
            updateCartCount();
        }
    }
}

// Calculate cart total
function calculateCartTotal() {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

// Show notification
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;

    // Add to body
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Hide and remove notification after 4 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 500);
    }, 4000);
}

// Format price
function formatPrice(price) {
    return price.toFixed(2) + ' ر.س';
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Get order status in Arabic
function getOrderStatus(status) {
    const statusMap = {
        'pending': 'قيد الانتظار',
        'processing': 'قيد المعالجة',
        'shipped': 'تم الشحن',
        'delivered': 'تم التوصيل',
        'cancelled': 'ملغي'
    };

    return statusMap[status] || status;
}

// Get category name in Arabic
function getCategoryName(category) {
    const categoryMap = {
        'men': 'ملابس رجالية',
        'women': 'ملابس نسائية',
        'children': 'ملابس أطفال'
    };

    return categoryMap[category] || category;
}

// Generate unique ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}