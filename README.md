# متجر الملابس الراقي

متجر إلكتروني متكامل لبيع الملابس باللغة العربية، مصمم بواجهة عصرية وسهلة الاستخدام.

## المميزات

### للمستخدمين
- عرض المنتجات مع إمكانية البحث والتصفية
- نظام سلة التسوق الكامل
- نظام طلبات مع تتبع الحالة
- نظام حسابات المستخدمين
- واجهة مستخدم متجاوبة تعمل على جميع الأجهزة

### للمسؤولين
- لوحة تحكم كاملة لإدارة المتجر
- إدارة المنتجات (إضافة، تعديل، حذف)
- إدارة الطلبات وتحديث حالتها
- إدارة المستخدمين
- عرض الإحصائيات والتقارير

## المتطلبات

- متصفح ويب حديث يدعم HTML5 و CSS3 و JavaScript
- لا حاجة لخادم (يعمل على المتصفح مباشرة)

## التثبيت والتشغيل

1. قم بتنزيل جميع ملفات المشروع
2. ضع الملفات في مجلد على خادم الويب المحلي أو استخدمها مباشرة من المتصفح
3. افتح ملف `index.html` في متصفحك

## بيانات الاعتماد الافتراضية

### حساب المسؤول
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: hala123

### حساب المستخدم
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: hala123

## هيكل الملفات

```
متجر الملابس الراقي/
├── css/
│   ├── admin.css      # أنماط لوحة تحكم المسؤول
│   └── style.css      # الأنماط الرئيسية
├── js/
│   ├── admin.js       # وظائف لوحة تحكم المسؤول
│   ├── auth.js        # وظائف المصادقة (تسجيل الدخول والتسجيل)
│   ├── cart.js        # وظائف سلة التسوق
│   ├── home.js        # وظائف الصفحة الرئيسية
│   ├── orders.js      # وظائف صفحة الطلبات
│   ├── products.js    # وظائف صفحة المنتجات
│   └── script.js      # الوظائف المشتركة
├── images/            # مجلد الصور (يجب إنشاؤه)
├── admin.html         # صفحة لوحة تحكم المسؤول
├── cart.html          # صفحة سلة التسوق
├── index.html         # الصفحة الرئيسية
├── login.html         # صفحة تسجيل الدخول
├── orders.html        # صفحة الطلبات
├── products.html      # صفحة المنتجات
└── README.md          # هذا الملف
```

## الوظائف

### الصفحة الرئيسية
- عرض المنتجات المميزة
- عرض الفئات الرئيسية
- واجهة مستخدم جذابة

### صفحة المنتجات
- عرض جميع المنتجات
- البحث عن المنتجات
- تصفية المنتجات حسب الفئة
- ترتيب المنتجات
- نظام ترقيم الصفحات

### سلة التسوق
- إضافة المنتجات إلى السلة
- تعديل كميات المنتجات
- حذف المنتجات من السلة
- حساب المجموع الفرعي والشحن والإجمالي
- عملية الدفع

### الطلبات
- عرض تاريخ الطلبات
- تفاصيل الطلب
- تتبع حالة الطلب

### تسجيل الدخول والتسجيل
- إنشاء حساب جديد
- تسجيل الدخول
- استعادة كلمة المرور (وهمية)

### لوحة تحكم المسؤول
- لوحة معلومات مع إحصائيات
- إدارة المنتجات
- إدارة الطلبات
- إدارة المستخدمين
- تقارير وإحصائيات

## التخزين

يستخدم المتجر التخزين المحلي (Local Storage) في المتصفح لحفظ:
- بيانات المنتجات
- بيانات المستخدمين
- الطلبات
- سلة التسوق
- جلسة المستخدم

## التوافق

- يعمل على جميع المتصفحات الحديثة
- متجاوب ويعمل على أجهزة الكمبيوتر والأجهزة اللوحية والهواتف الذكية
- يدعم اللغة العربية بالكامل (من اليمين إلى اليسار)

## التطوير المستقبلي

- إضافة بوابة دفع حقيقية
- ربط بقاعدة بيانات خارجية
- إضافة نظام تقييم المنتجات
- إضافة نظام خصومات وقسائم
- تحسين واجهة المستخدم وتجربة المستخدم

## المساهمون

- تم تطوير هذا المشروع كنموذج تعليمي

## الرخصة

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.
