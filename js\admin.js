// Admin dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is admin
    const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        isAdmin = currentUser && currentUser.role === 'admin';
    }

    if (!isAdmin) {
        window.location.href = 'login.html';
        return;
    }

    // Update admin name
    const adminName = document.getElementById('admin-name');
    if (adminName && currentUser) {
        adminName.textContent = currentUser.name;
    }

    // Setup admin menu
    setupAdminMenu();

    // Setup admin sections
    setupAdminSections();

    // Load dashboard data
    loadDashboardData();
});

// Setup admin menu
function setupAdminMenu() {
    const menuItems = document.querySelectorAll('.admin-menu-item');

    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // Get section ID
            const sectionId = this.getAttribute('data-section');

            // Update active menu item
            menuItems.forEach(mi => mi.classList.remove('active'));
            this.classList.add('active');

            // Show corresponding section
            const sections = document.querySelectorAll('.admin-section');
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === sectionId) {
                    section.classList.add('active');

                    // Load section data
                    switch (sectionId) {
                        case 'dashboard':
                            loadDashboardData();
                            break;
                        case 'products':
                            loadProductsData();
                            break;
                        case 'orders':
                            loadOrdersData();
                            break;
                        case 'customers':
                            loadCustomersData();
                            break;
                        case 'analytics':
                            loadAnalyticsData();
                            break;
                    }
                }
            });
        });
    });

    // Setup logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Setup admin sections
function setupAdminSections() {
    // Setup product modal
    setupProductModal();

    // Setup order status update
    setupOrderStatusUpdate();

    // Setup analytics filters
    setupAnalyticsFilters();
}

// Setup product modal
function setupProductModal() {
    // Add product button
    const addProductBtn = document.getElementById('add-product-btn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            openProductModal();
        });
    }

    // Product form
    const productForm = document.getElementById('product-form');
    if (productForm) {
        productForm.addEventListener('submit', handleProductSubmit);
    }

    // Modal close button
    const closeModalBtn = document.querySelector('#product-modal .close');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeProductModal);
    }

    // Close modal when clicking outside
    const modal = document.getElementById('product-modal');
    if (modal) {
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeProductModal();
            }
        });
    }

    // Setup image upload
    setupImageUpload();
}

// Setup order status update
function setupOrderStatusUpdate() {
    // This will be set up when orders are loaded
}

// Setup analytics filters
function setupAnalyticsFilters() {
    const generateReportBtn = document.getElementById('generate-report-btn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', generateReport);
    }
}

// Setup image upload
function setupImageUpload() {
    const uploadBtn = document.getElementById('upload-btn');
    const imageInput = document.getElementById('product-image');
    const previewImg = document.getElementById('preview-img');

    if (uploadBtn && imageInput && previewImg) {
        uploadBtn.addEventListener('click', function() {
            imageInput.click();
        });

        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    previewImg.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Load dashboard data
function loadDashboardData() {
    // Update stats
    document.getElementById('total-products').textContent = products.length;
    document.getElementById('total-orders').textContent = orders.length;
    document.getElementById('total-customers').textContent = users.filter(u => u.role === 'user').length;

    // Calculate total revenue
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    document.getElementById('total-revenue').textContent = formatPrice(totalRevenue);

    // Create charts (simplified for demo)
    createSalesChart();
    createProductsChart();
}

// Create sales chart
function createSalesChart() {
    const canvas = document.getElementById('sales-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // In a real application, this would use a charting library like Chart.js
    // For this demo, we'll just draw a simple bar chart

    // Sample data
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
    const sales = [12000, 19000, 15000, 25000, 22000, 30000];

    // Chart dimensions
    const margin = 40;
    const chartWidth = canvas.width - 2 * margin;
    const chartHeight = canvas.height - 2 * margin;
    const barWidth = chartWidth / months.length - 10;
    const maxSales = Math.max(...sales);

    // Draw axes
    ctx.beginPath();
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, canvas.height - margin);
    ctx.lineTo(canvas.width - margin, canvas.height - margin);
    ctx.strokeStyle = '#000';
    ctx.stroke();

    // Draw bars
    ctx.fillStyle = '#000';
    months.forEach((month, i) => {
        const barHeight = (sales[i] / maxSales) * chartHeight;
        const x = margin + i * (barWidth + 10) + 5;
        const y = canvas.height - margin - barHeight;

        ctx.fillRect(x, y, barWidth, barHeight);

        // Draw month label
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(month, x + barWidth / 2, canvas.height - margin + 20);

        // Draw sales value
        ctx.fillText(formatPrice(sales[i]), x + barWidth / 2, y - 5);

        ctx.fillStyle = '#000';
    });
}

// Create products chart
function createProductsChart() {
    const canvas = document.getElementById('products-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // In a real application, this would use a charting library like Chart.js
    // For this demo, we'll just draw a simple pie chart

    // Sample data
    const categories = ['ملابس رجالية', 'ملابس نسائية', 'ملابس أطفال'];
    const values = [35, 45, 20];
    const colors = ['#4e73df', '#1cc88a', '#36b9cc'];

    // Chart dimensions
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    // Draw pie chart
    let startAngle = 0;
    const total = values.reduce((sum, value) => sum + value, 0);

    values.forEach((value, i) => {
        const sliceAngle = (value / total) * 2 * Math.PI;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
        ctx.closePath();
        ctx.fillStyle = colors[i];
        ctx.fill();

        // Draw label
        const labelAngle = startAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

        ctx.fillStyle = '#fff';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${value}%`, labelX, labelY);

        startAngle += sliceAngle;
    });

    // Draw legend
    let legendY = 20;
    categories.forEach((category, i) => {
        ctx.fillStyle = colors[i];
        ctx.fillRect(canvas.width - 120, legendY, 15, 15);

        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(category, canvas.width - 100, legendY + 12);

        legendY += 25;
    });
}

// Load products data
function loadProductsData() {
    const tableBody = document.getElementById('products-table-body');

    if (!tableBody) {
        return;
    }

    // Clear table
    tableBody.innerHTML = '';

    // Add products to table
    products.forEach(product => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${product.id}</td>
            <td><img src="${product.image}" alt="${product.name}"></td>
            <td>${product.name}</td>
            <td>${getCategoryName(product.category)}</td>
            <td>${formatPrice(product.price)}</td>
            <td>${product.stock}</td>
            <td class="actions">
                <button class="edit-btn" data-id="${product.id}">تعديل</button>
                <button class="delete-btn" data-id="${product.id}">حذف</button>
            </td>
        `;

        // Add event listeners
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');

        editBtn.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-id'));
            editProduct(productId);
        });

        deleteBtn.addEventListener('click', function() {
            const productId = parseInt(this.getAttribute('data-id'));
            deleteProduct(productId);
        });

        tableBody.appendChild(row);
    });
}

// Open product modal
function openProductModal(product = null) {
    const modal = document.getElementById('product-modal');
    const modalTitle = document.getElementById('product-modal-title');
    const productForm = document.getElementById('product-form');

    if (!modal || !modalTitle || !productForm) {
        return;
    }

    // Reset form
    productForm.reset();

    if (product) {
        // Edit mode
        modalTitle.textContent = 'تعديل منتج';
        document.getElementById('product-id').value = product.id;
        document.getElementById('product-name').value = product.name;
        document.getElementById('product-category').value = product.category;
        document.getElementById('product-price').value = product.price;
        document.getElementById('product-stock').value = product.stock;
        document.getElementById('product-description').value = product.description;
        document.getElementById('product-image').value = product.image;
    } else {
        // Add mode
        modalTitle.textContent = 'إضافة منتج جديد';
        document.getElementById('product-id').value = '';
    }

    // Show modal
    modal.style.display = 'block';
}

// Close product modal
function closeProductModal() {
    const modal = document.getElementById('product-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Handle product submit
function handleProductSubmit(e) {
    e.preventDefault();

    const productId = document.getElementById('product-id').value;
    const name = document.getElementById('product-name').value;
    const category = document.getElementById('product-category').value;
    const price = parseFloat(document.getElementById('product-price').value);
    const stock = parseInt(document.getElementById('product-stock').value);
    const description = document.getElementById('product-description').value;
    const previewImg = document.getElementById('preview-img');

    // Get image as base64
    let image = '';
    if (previewImg && previewImg.src && !previewImg.src.includes('via.placeholder.com')) {
        image = previewImg.src;
    } else {
        // Use placeholder image if no image was uploaded
        image = 'https://via.placeholder.com/300x400?text=' + encodeURIComponent(name);
    }

    if (productId) {
        // Edit existing product
        const productIndex = products.findIndex(p => p.id == productId);

        if (productIndex !== -1) {
            products[productIndex] = {
                ...products[productIndex],
                name: name,
                category: category,
                price: price,
                stock: stock,
                description: description,
                image: image
            };

            showNotification('تم تعديل المنتج بنجاح');
        }
    } else {
        // Add new product
        const newProduct = {
            id: generateId(),
            name: name,
            category: category,
            price: price,
            stock: stock,
            description: description,
            image: image
        };

        products.push(newProduct);
        showNotification('تم إضافة المنتج بنجاح');
    }

    // Save data
    saveData();

    // Close modal
    closeProductModal();

    // Reload products data
    loadProductsData();
}

// Edit product
function editProduct(productId) {
    const product = products.find(p => p.id === productId);

    if (product) {
        openProductModal(product);
    }
}

// Delete product
function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        products = products.filter(p => p.id !== productId);

        // Save data
        saveData();

        // Show notification
        showNotification('تم حذف المنتج بنجاح');

        // Reload products data
        loadProductsData();
    }
}

// Load orders data
function loadOrdersData() {
    const tableBody = document.getElementById('orders-table-body');

    if (!tableBody) {
        return;
    }

    // Clear table
    tableBody.innerHTML = '';

    // Add orders to table (newest first)
    orders.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(order => {
        const row = document.createElement('tr');

        const statusClass = `status-${order.status}`;
        const statusText = getOrderStatus(order.status);

        row.innerHTML = `
            <td>#${order.id.substring(0, 8)}</td>
            <td>${order.customerName}</td>
            <td>${formatDate(order.date)}</td>
            <td>${formatPrice(order.total)}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td class="actions">
                <button class="view-btn" data-id="${order.id}">عرض</button>
                <select class="status-select" data-id="${order.id}">
                    <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>قيد الانتظار</option>
                    <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>قيد المعالجة</option>
                    <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>تم الشحن</option>
                    <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>تم التوصيل</option>
                    <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>ملغي</option>
                </select>
            </td>
        `;

        // Add event listeners
        const viewBtn = row.querySelector('.view-btn');
        const statusSelect = row.querySelector('.status-select');

        viewBtn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            viewOrderDetails(orderId);
        });

        statusSelect.addEventListener('change', function() {
            const orderId = this.getAttribute('data-id');
            const newStatus = this.value;
            updateOrderStatus(orderId, newStatus);
        });

        tableBody.appendChild(row);
    });
}

// View order details
function viewOrderDetails(orderId) {
    const order = orders.find(o => o.id === orderId);

    if (!order) {
        showNotification('الطلب غير موجود');
        return;
    }

    // In a real application, this would open a modal with order details
    // For this demo, we'll just show a notification
    showNotification(`عرض تفاصيل الطلب #${order.id.substring(0, 8)}`);
}

// Update order status
function updateOrderStatus(orderId, newStatus) {
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex !== -1) {
        orders[orderIndex].status = newStatus;

        // Save data
        saveData();

        // Show notification
        showNotification('تم تحديث حالة الطلب بنجاح');
    }
}

// Load customers data
function loadCustomersData() {
    const tableBody = document.getElementById('customers-table-body');

    if (!tableBody) {
        return;
    }

    // Clear table
    tableBody.innerHTML = '';

    // Get customers (users with role 'user')
    const customers = users.filter(u => u.role === 'user');

    // Add customers to table
    customers.forEach(customer => {
        const row = document.createElement('tr');

        // Get customer orders count
        const customerOrders = orders.filter(o => o.userId === customer.id);
        const ordersCount = customerOrders.length;

        row.innerHTML = `
            <td>${customer.id}</td>
            <td>${customer.name}</td>
            <td>${customer.email}</td>
            <td>${customer.phone}</td>
            <td>${ordersCount}</td>
            <td class="actions">
                <button class="view-btn" data-id="${customer.id}">عرض</button>
                <button class="delete-btn" data-id="${customer.id}">حذف</button>
            </td>
        `;

        // Add event listeners
        const viewBtn = row.querySelector('.view-btn');
        const deleteBtn = row.querySelector('.delete-btn');

        viewBtn.addEventListener('click', function() {
            const customerId = this.getAttribute('data-id');
            viewCustomerDetails(customerId);
        });

        deleteBtn.addEventListener('click', function() {
            const customerId = this.getAttribute('data-id');
            deleteCustomer(customerId);
        });

        tableBody.appendChild(row);
    });
}

// View customer details
function viewCustomerDetails(customerId) {
    const customer = users.find(u => u.id === customerId);

    if (!customer) {
        showNotification('العميل غير موجود');
        return;
    }

    // In a real application, this would open a modal with customer details
    // For this demo, we'll just show a notification
    showNotification(`عرض تفاصيل العميل ${customer.name}`);
}

// Delete customer
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        users = users.filter(u => u.id !== customerId);

        // Save data
        saveData();

        // Show notification
        showNotification('تم حذف العميل بنجاح');

        // Reload customers data
        loadCustomersData();
    }
}

// Load analytics data
function loadAnalyticsData() {
    // Generate initial report
    generateReport();
}

// Generate report
function generateReport() {
    const reportType = document.getElementById('report-type').value;
    const reportPeriod = document.getElementById('report-period').value;
    const reportTitle = document.getElementById('report-title');
    const reportSummary = document.getElementById('report-summary');

    if (!reportTitle || !reportSummary) {
        return;
    }

    // Update report title
    const typeMap = {
        'sales': 'تقرير المبيعات',
        'products': 'تقرير المنتجات',
        'customers': 'تقرير العملاء'
    };

    reportTitle.textContent = typeMap[reportType];

    // Generate report based on type
    switch (reportType) {
        case 'sales':
            generateSalesReport(reportPeriod, reportSummary);
            break;
        case 'products':
            generateProductsReport(reportPeriod, reportSummary);
            break;
        case 'customers':
            generateCustomersReport(reportPeriod, reportSummary);
            break;
    }

    // Create chart
    createAnalyticsChart(reportType, reportPeriod);
}

// Generate sales report
function generateSalesReport(period, summaryElement) {
    // In a real application, this would filter orders by period
    // For this demo, we'll use all orders

    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Count orders by status
    const statusCounts = {
        pending: orders.filter(o => o.status === 'pending').length,
        processing: orders.filter(o => o.status === 'processing').length,
        shipped: orders.filter(o => o.status === 'shipped').length,
        delivered: orders.filter(o => o.status === 'delivered').length,
        cancelled: orders.filter(o => o.status === 'cancelled').length
    };

    // Update summary
    summaryElement.innerHTML = `
        <div class="report-summary-item">
            <span>إجمالي الطلبات:</span>
            <span>${totalOrders}</span>
        </div>
        <div class="report-summary-item">
            <span>إجمالي الإيرادات:</span>
            <span>${formatPrice(totalRevenue)}</span>
        </div>
        <div class="report-summary-item">
            <span>متوسط قيمة الطلب:</span>
            <span>${formatPrice(averageOrderValue)}</span>
        </div>
        <div class="report-summary-item">
            <span>الطلبات قيد الانتظار:</span>
            <span>${statusCounts.pending}</span>
        </div>
        <div class="report-summary-item">
            <span>الطلبات قيد المعالجة:</span>
            <span>${statusCounts.processing}</span>
        </div>
        <div class="report-summary-item">
            <span>الطلبات المرسلة:</span>
            <span>${statusCounts.shipped}</span>
        </div>
        <div class="report-summary-item">
            <span>الطلبات المسلمة:</span>
            <span>${statusCounts.delivered}</span>
        </div>
        <div class="report-summary-item">
            <span>الطلبات الملغية:</span>
            <span>${statusCounts.cancelled}</span>
        </div>
    `;
}

// Generate products report
function generateProductsReport(period, summaryElement) {
    // In a real application, this would filter products by period
    // For this demo, we'll use all products

    const totalProducts = products.length;
    const totalStock = products.reduce((sum, product) => sum + product.stock, 0);
    const averagePrice = totalProducts > 0 ? products.reduce((sum, product) => sum + product.price, 0) / totalProducts : 0;

    // Count products by category
    const categoryCounts = {
        men: products.filter(p => p.category === 'men').length,
        women: products.filter(p => p.category === 'women').length,
        children: products.filter(p => p.category === 'children').length
    };

    // Find low stock products (less than 5)
    const lowStockProducts = products.filter(p => p.stock < 5);

    // Update summary
    summaryElement.innerHTML = `
        <div class="report-summary-item">
            <span>إجمالي المنتجات:</span>
            <span>${totalProducts}</span>
        </div>
        <div class="report-summary-item">
            <span>إجمالي المخزون:</span>
            <span>${totalStock}</span>
        </div>
        <div class="report-summary-item">
            <span>متوسط السعر:</span>
            <span>${formatPrice(averagePrice)}</span>
        </div>
        <div class="report-summary-item">
            <span>المنتجات الرجالية:</span>
            <span>${categoryCounts.men}</span>
        </div>
        <div class="report-summary-item">
            <span>المنتجات النسائية:</span>
            <span>${categoryCounts.women}</span>
        </div>
        <div class="report-summary-item">
            <span>منتجات الأطفال:</span>
            <span>${categoryCounts.children}</span>
        </div>
        <div class="report-summary-item">
            <span>المنتجات منخفضة المخزون:</span>
            <span>${lowStockProducts.length}</span>
        </div>
    `;
}

// Generate customers report
function generateCustomersReport(period, summaryElement) {
    // In a real application, this would filter customers by period
    // For this demo, we'll use all customers

    const customers = users.filter(u => u.role === 'user');
    const totalCustomers = customers.length;

    // Get customer orders
    const customerOrders = orders.filter(order => order.userId);

    // Calculate average orders per customer
    const averageOrdersPerCustomer = totalCustomers > 0 ? customerOrders.length / totalCustomers : 0;

    // Calculate total revenue per customer
    const totalRevenue = customerOrders.reduce((sum, order) => sum + order.total, 0);
    const averageRevenuePerCustomer = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    // Find top customers (by number of orders)
    const customerOrderCounts = {};
    customerOrders.forEach(order => {
        if (!customerOrderCounts[order.userId]) {
            customerOrderCounts[order.userId] = 0;
        }
        customerOrderCounts[order.userId]++;
    });

    const topCustomers = Object.entries(customerOrderCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([userId, count]) => {
            const customer = customers.find(c => c.id === userId);
            return {
                name: customer ? customer.name : 'Unknown',
                orders: count
            };
        });

    // Update summary
    summaryElement.innerHTML = `
        <div class="report-summary-item">
            <span>إجمالي العملاء:</span>
            <span>${totalCustomers}</span>
        </div>
        <div class="report-summary-item">
            <span>متوسط الطلبات لكل عميل:</span>
            <span>${averageOrdersPerCustomer.toFixed(1)}</span>
        </div>
        <div class="report-summary-item">
            <span>متوسط الإيرادات لكل عميل:</span>
            <span>${formatPrice(averageRevenuePerCustomer)}</span>
        </div>
        <div class="report-summary-item">
            <span>أفضل العملاء:</span>
            <span>${topCustomers.map(c => `${c.name} (${c.orders} طلب)`).join(', ')}</span>
        </div>
    `;
}

// Create analytics chart
function createAnalyticsChart(reportType, reportPeriod) {
    const canvas = document.getElementById('analytics-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // In a real application, this would use a charting library like Chart.js
    // For this demo, we'll just draw a simple chart based on report type

    switch (reportType) {
        case 'sales':
            createSalesAnalyticsChart(ctx, canvas, reportPeriod);
            break;
        case 'products':
            createProductsAnalyticsChart(ctx, canvas, reportPeriod);
            break;
        case 'customers':
            createCustomersAnalyticsChart(ctx, canvas, reportPeriod);
            break;
    }
}

// Create sales analytics chart
function createSalesAnalyticsChart(ctx, canvas, period) {
    // Sample data
    let labels, data;

    switch (period) {
        case 'week':
            labels = ['السبت', 'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
            data = [5000, 7000, 6000, 8000, 7500, 9000, 12000];
            break;
        case 'month':
            labels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
            data = [25000, 30000, 28000, 35000];
            break;
        case 'quarter':
            labels = ['يناير', 'فبراير', 'مارس'];
            data = [80000, 95000, 110000];
            break;
        case 'year':
            labels = ['Q1', 'Q2', 'Q3', 'Q4'];
            data = [285000, 320000, 310000, 350000];
            break;
    }

    // Draw line chart
    const margin = 40;
    const chartWidth = canvas.width - 2 * margin;
    const chartHeight = canvas.height - 2 * margin;
    const maxValue = Math.max(...data);

    // Draw axes
    ctx.beginPath();
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, canvas.height - margin);
    ctx.lineTo(canvas.width - margin, canvas.height - margin);
    ctx.strokeStyle = '#000';
    ctx.stroke();

    // Draw line
    ctx.beginPath();
    ctx.moveTo(margin, canvas.height - margin);

    data.forEach((value, i) => {
        const x = margin + (i / (data.length - 1)) * chartWidth;
        const y = canvas.height - margin - (value / maxValue) * chartHeight;

        ctx.lineTo(x, y);

        // Draw point
        ctx.fillStyle = '#000';
        ctx.beginPath();
        ctx.arc(x, y, 5, 0, Math.PI * 2);
        ctx.fill();

        // Draw label
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(labels[i], x, canvas.height - margin + 20);

        // Draw value
        ctx.fillText(formatPrice(value), x, y - 10);
    });

    ctx.strokeStyle = '#000';
    ctx.stroke();
}

// Create products analytics chart
function createProductsAnalyticsChart(ctx, canvas, period) {
    // Sample data
    const categories = ['ملابس رجالية', 'ملابس نسائية', 'ملابس أطفال'];
    const values = [35, 45, 20];
    const colors = ['#4e73df', '#1cc88a', '#36b9cc'];

    // Draw pie chart
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;

    let startAngle = 0;
    const total = values.reduce((sum, value) => sum + value, 0);

    values.forEach((value, i) => {
        const sliceAngle = (value / total) * 2 * Math.PI;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
        ctx.closePath();
        ctx.fillStyle = colors[i];
        ctx.fill();

        // Draw label
        const labelAngle = startAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

        ctx.fillStyle = '#fff';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${value}%`, labelX, labelY);

        startAngle += sliceAngle;
    });

    // Draw legend
    let legendY = 20;
    categories.forEach((category, i) => {
        ctx.fillStyle = colors[i];
        ctx.fillRect(canvas.width - 120, legendY, 15, 15);

        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(category, canvas.width - 100, legendY + 12);

        legendY += 25;
    });
}

// Create customers analytics chart
function createCustomersAnalyticsChart(ctx, canvas, period) {
    // Sample data
    let labels, data;

    switch (period) {
        case 'week':
            labels = ['السبت', 'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
            data = [5, 8, 6, 10, 7, 12, 15];
            break;
        case 'month':
            labels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
            data = [25, 30, 28, 35];
            break;
        case 'quarter':
            labels = ['يناير', 'فبراير', 'مارس'];
            data = [80, 95, 110];
            break;
        case 'year':
            labels = ['Q1', 'Q2', 'Q3', 'Q4'];
            data = [285, 320, 310, 350];
            break;
    }

    // Draw bar chart
    const margin = 40;
    const chartWidth = canvas.width - 2 * margin;
    const chartHeight = canvas.height - 2 * margin;
    const barWidth = chartWidth / labels.length - 10;
    const maxValue = Math.max(...data);

    // Draw axes
    ctx.beginPath();
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, canvas.height - margin);
    ctx.lineTo(canvas.width - margin, canvas.height - margin);
    ctx.strokeStyle = '#000';
    ctx.stroke();

    // Draw bars
    ctx.fillStyle = '#000';
    labels.forEach((label, i) => {
        const barHeight = (data[i] / maxValue) * chartHeight;
        const x = margin + i * (barWidth + 10) + 5;
        const y = canvas.height - margin - barHeight;

        ctx.fillRect(x, y, barWidth, barHeight);

        // Draw label
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(label, x + barWidth / 2, canvas.height - margin + 20);

        // Draw value
        ctx.fillText(data[i], x + barWidth / 2, y - 5);

        ctx.fillStyle = '#000';
    });
}