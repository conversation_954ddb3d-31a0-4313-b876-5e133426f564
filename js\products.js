// Products page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const categoryParam = urlParams.get('category');

    // Set category filter if provided
    if (categoryParam) {
        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.value = categoryParam;
        }
    }

    // Load products
    loadProducts();

    // Setup event listeners
    setupProductsEventListeners();
});

// Setup products page event listeners
function setupProductsEventListeners() {
    // Search input
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', filterProducts);
    }

    // Category filter
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterProducts);
    }

    // Sort filter
    const sortFilter = document.getElementById('sort-filter');
    if (sortFilter) {
        sortFilter.addEventListener('change', sortProducts);
    }

    // Pagination
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');

    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => changePage(-1));
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => changePage(1));
    }
}

// Load products
let currentPage = 1;
const productsPerPage = 8;
let filteredProducts = [...products];

function loadProducts() {
    const productsContainer = document.getElementById('products-container');

    if (!productsContainer) {
        return;
    }

    // Apply filters
    filterProducts();

    // Clear container
    productsContainer.innerHTML = '';

    // Calculate pagination
    const startIndex = (currentPage - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    // Add products to container
    paginatedProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsContainer.appendChild(productCard);
    });

    // Update pagination
    updatePagination();
}

// Create product card
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';

    card.innerHTML = `
        <img src="${product.image}" alt="${product.name}">
        <div class="product-info">
            <h3>${product.name}</h3>
            <div class="price">${formatPrice(product.price)}</div>
            <p class="description">${product.description.substring(0, 100)}...</p>
            <button class="add-to-cart" data-id="${product.id}">أضف إلى السلة</button>
        </div>
    `;

    // Add event listener to add to cart button
    const addToCartBtn = card.querySelector('.add-to-cart');
    addToCartBtn.addEventListener('click', function() {
        const productId = parseInt(this.getAttribute('data-id'));
        addToCart(productId);
    });

    return card;
}

// Filter products
function filterProducts() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');

    let filtered = [...products];

    // Apply search filter
    if (searchInput && searchInput.value.trim() !== '') {
        const searchTerm = searchInput.value.toLowerCase();
        filtered = filtered.filter(product => 
            product.name.toLowerCase().includes(searchTerm) || 
            product.description.toLowerCase().includes(searchTerm)
        );
    }

    // Apply category filter
    if (categoryFilter && categoryFilter.value !== 'all') {
        filtered = filtered.filter(product => product.category === categoryFilter.value);
    }

    filteredProducts = filtered;
    currentPage = 1;

    // Reload products
    loadProducts();
}

// Sort products
function sortProducts() {
    const sortFilter = document.getElementById('sort-filter');

    if (!sortFilter) {
        return;
    }

    const sortValue = sortFilter.value;

    switch (sortValue) {
        case 'price-low':
            filteredProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filteredProducts.sort((a, b) => b.price - a.price);
            break;
        case 'name':
            filteredProducts.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
            break;
        default:
            // Default order (by ID)
            filteredProducts.sort((a, b) => a.id - b.id);
    }

    // Reload products
    loadProducts();
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    const pageNumbers = document.getElementById('page-numbers');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');

    if (pageNumbers) {
        pageNumbers.textContent = `صفحة ${currentPage} من ${totalPages}`;
    }

    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage === 1;
    }

    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage === totalPages || totalPages === 0;
    }
}

// Change page
function changePage(direction) {
    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

    currentPage += direction;

    if (currentPage < 1) {
        currentPage = 1;
    } else if (currentPage > totalPages) {
        currentPage = totalPages;
    }

    loadProducts();
}