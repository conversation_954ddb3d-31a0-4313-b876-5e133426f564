// Home page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load featured products
    loadFeaturedProducts();
});

// Load featured products
function loadFeaturedProducts() {
    const featuredProductsContainer = document.getElementById('featured-products');

    if (!featuredProductsContainer) {
        return;
    }

    // Get 4 random products
    const featuredProducts = [...products].sort(() => 0.5 - Math.random()).slice(0, 4);

    // Clear container
    featuredProductsContainer.innerHTML = '';

    // Add products to container
    featuredProducts.forEach(product => {
        const productCard = createProductCard(product);
        featuredProductsContainer.appendChild(productCard);
    });
}

// Create product card
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';

    card.innerHTML = `
        <img src="${product.image}" alt="${product.name}">
        <div class="product-info">
            <h3>${product.name}</h3>
            <div class="price">${formatPrice(product.price)}</div>
            <p class="description">${product.description.substring(0, 100)}...</p>
            <button class="add-to-cart" data-id="${product.id}">أضف إلى السلة</button>
        </div>
    `;

    // Add event listener to add to cart button
    const addToCartBtn = card.querySelector('.add-to-cart');
    addToCartBtn.addEventListener('click', function() {
        const productId = parseInt(this.getAttribute('data-id'));
        addToCart(productId);
    });

    return card;
}