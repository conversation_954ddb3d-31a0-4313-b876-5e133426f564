/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    direction: rtl;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.btn {
    display: inline-block;
    background-color: #000;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: #333;
}

.text-center {
    text-align: center;
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.logo h1 {
    font-size: 24px;
    color: #000;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-right: 20px;
}

nav ul li a {
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000;
    font-weight: bold;
}

/* Hero Section */
.hero {
    background-color: #f9f9f9;
    padding: 80px 0;
    text-align: center;
}

.hero h2 {
    font-size: 36px;
    margin-bottom: 20px;
}

.hero p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Categories Section */
.featured-categories {
    padding: 60px 0;
}

.featured-categories h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 32px;
}

.categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.category {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category:hover img {
    transform: scale(1.05);
}

.category h3 {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 15px;
    text-align: center;
    font-size: 20px;
}

/* Products Section */
.featured-products {
    padding: 60px 0;
    background-color: #f9f9f9;
}

.featured-products h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 32px;
}

.products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.product-info {
    padding: 15px;
}

.product-info h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.product-info .price {
    font-weight: bold;
    color: #000;
    margin-bottom: 10px;
}

.product-info .description {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.add-to-cart {
    width: 100%;
    background-color: #000;
    color: #fff;
    border: none;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.add-to-cart:hover {
    background-color: #333;
}

/* Page Header */
.page-header {
    background-color: #f9f9f9;
    padding: 40px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 32px;
    margin-bottom: 10px;
}

.page-header p {
    font-size: 16px;
    color: #666;
}

/* Products Section */
.products-section {
    padding: 60px 0;
}

.products-filter {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    width: 300px;
}

.search-box input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
}

.search-box button {
    background-color: #000;
    color: #fff;
    border: none;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.filter-options {
    display: flex;
    gap: 15px;
}

.filter-options select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    gap: 10px;
}

.pagination button {
    padding: 8px 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.pagination button:hover {
    background-color: #eee;
}

/* Cart Section */
.cart-section {
    padding: 60px 0;
}

.cart-container {
    display: flex;
    gap: 30px;
}

.cart-items {
    flex: 2;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.cart-item img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
    margin-left: 20px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-info h3 {
    margin-bottom: 10px;
}

.cart-item-info .price {
    font-weight: bold;
    margin-bottom: 10px;
}

.cart-item-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-control {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.quantity-control button {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 18px;
}

.quantity-control input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: none;
    border-right: 1px solid #ddd;
    border-left: 1px solid #ddd;
}

.remove-item {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    font-size: 18px;
}

.cart-summary {
    flex: 1;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    height: fit-content;
}

.cart-summary h3 {
    margin-bottom: 20px;
    font-size: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.summary-item.total {
    font-weight: bold;
    font-size: 18px;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.checkout-btn, .continue-shopping {
    width: 100%;
    margin-bottom: 10px;
}

.continue-shopping {
    background-color: #f9f9f9;
    color: #000;
    border: 1px solid #ddd;
}

.continue-shopping:hover {
    background-color: #eee;
}

.empty-cart {
    text-align: center;
    padding: 60px 20px;
}

.empty-cart i {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-cart h3 {
    margin-bottom: 10px;
}

.empty-cart p {
    color: #666;
    margin-bottom: 20px;
}

/* Orders Section */
.orders-section {
    padding: 60px 0;
}

.order-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.order-header h3 {
    font-size: 18px;
}

.order-status {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-shipped {
    background-color: #d4edda;
    color: #155724;
}

.status-delivered {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.order-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.order-details div {
    flex: 1;
}

.order-details h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.order-details p {
    font-size: 16px;
}

.order-items {
    margin-bottom: 15px;
}

.order-items h4 {
    margin-bottom: 10px;
}

.order-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.order-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    margin-left: 10px;
}

.order-item-info {
    flex: 1;
}

.order-item-info h5 {
    font-size: 14px;
    margin-bottom: 5px;
}

.order-item-info p {
    font-size: 12px;
    color: #666;
}

.order-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.view-order, .track-order {
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.view-order {
    background-color: #000;
    color: #fff;
    border: none;
}

.track-order {
    background-color: #f9f9f9;
    color: #000;
    border: 1px solid #ddd;
}

.no-orders {
    text-align: center;
    padding: 60px 20px;
}

.no-orders i {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.no-orders h3 {
    margin-bottom: 10px;
}

.no-orders p {
    color: #666;
    margin-bottom: 20px;
}

/* Auth Section */
.auth-section {
    padding: 60px 0;
    background-color: #f9f9f9;
}

.auth-container {
    max-width: 500px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-tabs {
    display: flex;
}

.auth-tab {
    flex: 1;
    padding: 15px;
    background-color: #f9f9f9;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

.auth-tab.active {
    background-color: #fff;
}

.auth-form {
    padding: 30px;
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.image-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.image-preview {
    width: 200px;
    height: 200px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

#product-image {
    display: none;
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me input {
    margin-left: 10px;
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: #000;
    font-weight: bold;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 24px;
    cursor: pointer;
}

/* Footer */
footer {
    background-color: #000;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 20px;
    margin-bottom: 20px;
}

.footer-section p {
    color: #ccc;
    margin-bottom: 20px;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ccc;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fff;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #fff;
    transition: background-color 0.3s ease;
}

.social-links li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #ccc;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .cart-container {
        flex-direction: column;
    }

    .cart-summary {
        width: 100%;
    }
}

@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }

    nav ul {
        margin-top: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }

    nav ul li {
        margin: 5px 10px;
    }

    .hero h2 {
        font-size: 28px;
    }

    .hero p {
        font-size: 16px;
    }

    .featured-categories h2,
    .featured-products h2,
    .page-header h1 {
        font-size: 28px;
    }

    .products-filter {
        flex-direction: column;
        gap: 15px;
    }

    .search-box {
        width: 100%;
    }

    .filter-options {
        width: 100%;
        justify-content: space-between;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .order-details {
        flex-direction: column;
    }

    .order-details div {
        margin-bottom: 10px;
    }

    .order-actions {
        flex-direction: column;
    }

    .view-order, .track-order {
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 10px;
    }

    .hero {
        padding: 40px 0;
    }

    .hero h2 {
        font-size: 24px;
    }

    .featured-categories,
    .featured-products,
    .products-section,
    .cart-section,
    .orders-section,
    .auth-section {
        padding: 40px 0;
    }

    .categories,
    .products {
        grid-template-columns: 1fr;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .cart-item img {
        margin-left: 0;
        margin-bottom: 15px;
        width: 100%;
        height: 200px;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: #000;
    color: #fff;
    padding: 15px 25px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    transition: all 0.4s ease;
    max-width: 80%;
    text-align: center;
}

.notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}