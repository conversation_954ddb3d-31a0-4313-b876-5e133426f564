// Orders page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Load orders
    loadOrders();

    // Setup event listeners
    setupOrdersEventListeners();
});

// Setup orders page event listeners
function setupOrdersEventListeners() {
    // Modal close button
    const closeModalBtn = document.querySelector('.close');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', closeOrderDetailsModal);
    }

    // Close modal when clicking outside
    const modal = document.getElementById('order-details-modal');
    if (modal) {
        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeOrderDetailsModal();
            }
        });
    }
}

// Load orders
function loadOrders() {
    const ordersContainer = document.getElementById('orders-container');
    const noOrders = document.getElementById('no-orders');

    if (!ordersContainer || !noOrders) {
        return;
    }

    // Clear container
    ordersContainer.innerHTML = '';

    // Get user orders
    let userOrders = [];

    if (currentUser) {
        userOrders = orders.filter(order => order.userId === currentUser.id);
    }

    // Check if user has orders
    if (userOrders.length === 0) {
        noOrders.style.display = 'block';
        return;
    }

    noOrders.style.display = 'none';

    // Add orders to container (newest first)
    userOrders.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(order => {
        const orderCard = createOrderCard(order);
        ordersContainer.appendChild(orderCard);
    });
}

// Create order card
function createOrderCard(order) {
    const orderCard = document.createElement('div');
    orderCard.className = 'order-card';

    const statusClass = `status-${order.status}`;
    const statusText = getOrderStatus(order.status);

    orderCard.innerHTML = `
        <div class="order-header">
            <h3>طلب #${order.id.substring(0, 8)}</h3>
            <span class="order-status ${statusClass}">${statusText}</span>
        </div>
        <div class="order-details">
            <div>
                <h4>التاريخ</h4>
                <p>${formatDate(order.date)}</p>
            </div>
            <div>
                <h4>المبلغ</h4>
                <p>${formatPrice(order.total)}</p>
            </div>
            <div>
                <h4>طريقة الدفع</h4>
                <p>${order.paymentMethod === 'cash' ? 'الدفع عند الاستلام' : 'بطاقة ائتمان'}</p>
            </div>
        </div>
        <div class="order-items">
            <h4>المنتجات (${order.items.length})</h4>
            <div class="order-items-list">
                ${order.items.map(item => `
                    <div class="order-item">
                        <img src="${item.image}" alt="${item.name}">
                        <div class="order-item-info">
                            <h5>${item.name}</h5>
                            <p>${formatPrice(item.price)} × ${item.quantity}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="order-actions">
            <button class="view-order" data-id="${order.id}">عرض التفاصيل</button>
            ${order.status === 'shipped' ? `<button class="track-order" data-id="${order.id}">تتبع الطلب</button>` : ''}
        </div>
    `;

    // Add event listeners
    const viewOrderBtn = orderCard.querySelector('.view-order');
    if (viewOrderBtn) {
        viewOrderBtn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            showOrderDetails(orderId);
        });
    }

    const trackOrderBtn = orderCard.querySelector('.track-order');
    if (trackOrderBtn) {
        trackOrderBtn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-id');
            trackOrder(orderId);
        });
    }

    return orderCard;
}

// Show order details
function showOrderDetails(orderId) {
    const order = orders.find(o => o.id === orderId);

    if (!order) {
        showNotification('الطلب غير موجود');
        return;
    }

    const modal = document.getElementById('order-details-modal');
    const modalContent = document.getElementById('order-details-content');

    if (!modal || !modalContent) {
        return;
    }

    const statusClass = `status-${order.status}`;
    const statusText = getOrderStatus(order.status);

    modalContent.innerHTML = `
        <div class="order-details-header">
            <h3>طلب #${order.id.substring(0, 8)}</h3>
            <span class="order-status ${statusClass}">${statusText}</span>
        </div>

        <div class="order-details-section">
            <h4>معلومات العميل</h4>
            <div class="customer-info">
                <p><strong>الاسم:</strong> ${order.customerName}</p>
                <p><strong>البريد الإلكتروني:</strong> ${order.customerEmail}</p>
                <p><strong>الهاتف:</strong> ${order.customerPhone}</p>
                <p><strong>العنوان:</strong> ${order.customerAddress}, ${order.customerCity}</p>
            </div>
        </div>

        <div class="order-details-section">
            <h4>معلومات الطلب</h4>
            <div class="order-info">
                <p><strong>التاريخ:</strong> ${formatDate(order.date)}</p>
                <p><strong>طريقة الدفع:</strong> ${order.paymentMethod === 'cash' ? 'الدفع عند الاستلام' : 'بطاقة ائتمان'}</p>
                <p><strong>الحالة:</strong> ${statusText}</p>
            </div>
        </div>

        <div class="order-details-section">
            <h4>المنتجات</h4>
            <div class="order-items-details">
                <table class="order-items-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.items.map(item => `
                            <tr>
                                <td>
                                    <div class="order-item-cell">
                                        <img src="${item.image}" alt="${item.name}">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>${formatPrice(item.price)}</td>
                                <td>${item.quantity}</td>
                                <td>${formatPrice(item.price * item.quantity)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="order-details-section">
            <h4>ملخص الدفع</h4>
            <div class="payment-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatPrice(order.subtotal)}</span>
                </div>
                <div class="summary-row">
                    <span>رسوم التوصيل:</span>
                    <span>${formatPrice(order.shipping)}</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع:</span>
                    <span>${formatPrice(order.total)}</span>
                </div>
            </div>
        </div>
    `;

    // Show modal
    modal.style.display = 'block';
}

// Close order details modal
function closeOrderDetailsModal() {
    const modal = document.getElementById('order-details-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Track order
function trackOrder(orderId) {
    const order = orders.find(o => o.id === orderId);

    if (!order) {
        showNotification('الطلب غير موجود');
        return;
    }

    // In a real application, this would show tracking information
    // For this demo, we'll just show a notification
    showNotification('جاري تحميل معلومات التتبع...');

    // Simulate loading tracking information
    setTimeout(() => {
        showNotification('الطلب في طريقه إليك! موعد التسليم المتوقع: بعد يومين');
    }, 1500);
}