/* Admin Dashboard Styles */
.admin-main {
    background-color: #f9f9f9;
    min-height: calc(100vh - 80px);
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.admin-container {
    display: flex;
    min-height: calc(100vh - 80px);
}

.admin-sidebar {
    width: 250px;
    background-color: #000;
    color: #fff;
    padding: 20px 0;
}

.admin-user {
    display: flex;
    align-items: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.user-avatar {
    font-size: 40px;
    margin-left: 15px;
}

.user-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.user-info p {
    color: #ccc;
    font-size: 14px;
}

.admin-menu {
    padding: 0;
}

.admin-menu li {
    margin-bottom: 5px;
}

.admin-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #fff;
    transition: background-color 0.3s ease;
}

.admin-menu-item i {
    margin-left: 10px;
    font-size: 18px;
}

.admin-menu-item:hover,
.admin-menu-item.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.admin-menu li:last-child a {
    margin-top: 20px;
    color: #e74c3c;
}

.admin-content {
    flex: 1;
    padding: 20px;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 28px;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: #f9f9f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.stat-icon i {
    font-size: 24px;
    color: #000;
}

.stat-info h3 {
    font-size: 24px;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
}

/* Dashboard Charts */
.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
}

.chart-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
    margin-bottom: 20px;
    font-size: 18px;
}

/* Admin Tables */
.admin-table-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.admin-table th {
    background-color: #f9f9f9;
    font-weight: bold;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

.admin-table img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.admin-table .actions {
    display: flex;
    gap: 10px;
}

.admin-table .actions button {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.edit-btn {
    background-color: #f0ad4e;
    color: #fff;
}

.delete-btn {
    background-color: #d9534f;
    color: #fff;
}

.view-btn {
    background-color: #5bc0de;
    color: #fff;
}

/* Analytics Section */
.analytics-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: bold;
}

.filter-group select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.analytics-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.report-summary {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.report-summary h3 {
    margin-bottom: 20px;
}

.report-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.report-summary-item:last-child {
    border-bottom: none;
    font-weight: bold;
}

/* Order Status Badge */
.status-badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-shipped {
    background-color: #d4edda;
    color: #155724;
}

.status-delivered {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .admin-container {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .admin-user {
        padding: 0 20px 10px;
    }

    .admin-menu {
        display: flex;
        overflow-x: auto;
        padding: 0 10px;
    }

    .admin-menu li {
        margin-bottom: 0;
        margin-left: 10px;
    }

    .dashboard-charts,
    .analytics-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .analytics-filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .admin-table-container {
        overflow-x: auto;
    }

    .admin-table {
        min-width: 600px;
    }
}