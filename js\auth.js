// Authentication page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Setup auth tabs
    setupAuthTabs();

    // Setup forms
    setupAuthForms();
});

// Setup auth tabs
function setupAuthTabs() {
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (!loginTab || !registerTab || !loginForm || !registerForm) {
        return;
    }

    // Login tab click
    loginTab.addEventListener('click', function() {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.classList.add('active');
        registerForm.classList.remove('active');
    });

    // Register tab click
    registerTab.addEventListener('click', function() {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        registerForm.classList.add('active');
        loginForm.classList.remove('active');
    });
}

// Setup auth forms
function setupAuthForms() {
    // Login form
    const loginForm = document.getElementById('login-user-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Register form
    const registerForm = document.getElementById('register-user-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // Forgot password link
    const forgotPasswordLink = document.getElementById('forgot-password');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }
}

// Handle login
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    const rememberMe = document.getElementById('remember-me').checked;

    // Find user with matching email and password
    const user = users.find(u => u.email === email && u.password === password);

    if (user) {
        // Set current user
        currentUser = {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            role: user.role
        };

        isAdmin = user.role === 'admin';

        // Save to localStorage
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        // Also save to sessionStorage as backup
        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));

        // Show success message
        showNotification('تم تسجيل الدخول بنجاح!');

        // Redirect to appropriate page
        setTimeout(() => {
            if (isAdmin) {
                window.location.href = 'admin.html';
            } else {
                window.location.href = 'index.html';
            }
        }, 2000);
    } else {
        // Show error message
        showNotification('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
}

// Handle register
function handleRegister(e) {
    e.preventDefault();

    const name = document.getElementById('register-name').value;
    const email = document.getElementById('register-email').value;
    const phone = document.getElementById('register-phone').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const termsAccepted = document.getElementById('register-terms').checked;

    // Validate passwords match
    if (password !== confirmPassword) {
        showNotification('كلمتا المرور غير متطابقتين');
        return;
    }

    // Check if email already exists
    if (users.some(u => u.email === email)) {
        showNotification('البريد الإلكتروني مستخدم بالفعل');
        return;
    }

    // Check terms acceptance
    if (!termsAccepted) {
        showNotification('يجب الموافقة على الشروط والأحكام');
        return;
    }

    // Create new user
    const newUser = {
        id: generateId(),
        name: name,
        email: email,
        phone: phone,
        password: password,
        role: 'user'
    };

    // Add to users array
    users.push(newUser);

    // Save to localStorage
    saveData();

    // Set current user
    currentUser = {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
        phone: newUser.phone,
        role: newUser.role
    };

    isAdmin = false;

    // Save to localStorage
    localStorage.setItem('currentUser', JSON.stringify(currentUser));

    // Show success message
    showNotification('تم إنشاء الحساب بنجاح!');

    // Redirect to home page
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Handle forgot password
function handleForgotPassword(e) {
    e.preventDefault();

    const email = prompt('أدخل بريدك الإلكتروني لإرسال رابط استعادة كلمة المرور:');

    if (!email) {
        return;
    }

    // Check if email exists
    const user = users.find(u => u.email === email);

    if (user) {
        // In a real application, this would send an email
        // For this demo, we'll just show a notification
        showNotification('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني');
    } else {
        showNotification('البريد الإلكتروني غير مسجل لدينا');
    }
}